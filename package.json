{"name": "amqplus.moe", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "format": "prettier --write .", "lint": "prettier --check ."}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "bits-ui": "^2.8.10", "clsx": "^2.1.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.4", "vite": "^6.2.6"}, "dependencies": {"@resvg/resvg-js": "^2.6.2", "@sveltejs/adapter-node": "^5.2.13", "@xyflow/svelte": "^1.2.1", "dotenv": "^16.5.0", "jsdom": "^26.1.0", "lucide-svelte": "^0.525.0", "mini-svg-data-uri": "^1.4.4", "satori": "^0.13.2", "satori-html": "^0.3.2", "svelte-motion": "^0.12.2", "svelte-range-slider-pips": "^4.0.7"}}