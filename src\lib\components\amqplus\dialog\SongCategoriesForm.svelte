<script>
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';

	let { 
		editedValue = $bindable(),
		config,
		isValid = $bindable(true),
		validationMessage = $bindable('')
	} = $props();

	// Ensure editedValue has proper structure
	$effect(() => {
		if (!editedValue || typeof editedValue !== 'object') {
			editedValue = {
				standard: { openings: true, endings: true, inserts: true },
				instrumental: { openings: true, endings: true, inserts: true },
				chanting: { openings: true, endings: true, inserts: true },
				character: { openings: true, endings: true, inserts: true }
			};
		}
		
		// Ensure all categories exist
		['standard', 'instrumental', 'chanting', 'character'].forEach(category => {
			if (!editedValue[category]) {
				editedValue[category] = { openings: true, endings: true, inserts: true };
			}
		});
	});

	function handleCategoryChange(category, songType, checked) {
		if (!editedValue[category]) {
			editedValue[category] = {};
		}
		editedValue[category][songType] = checked;
		
		// Always valid for now
		isValid = true;
		validationMessage = '';
	}
</script>

<div class="space-y-4">
	<Label class="text-base font-medium">Song Categories</Label>
	<p class="text-sm text-gray-600">Select which song categories to include for each song type</p>
	
	<div class="grid grid-cols-1 gap-4">
		{#each [
			{ key: 'standard', label: 'Standard', desc: 'Regular songs with vocals' },
			{ key: 'instrumental', label: 'Instrumental', desc: 'Songs without vocals' },
			{ key: 'chanting', label: 'Chanting', desc: 'Songs with chanting or repetitive vocals' },
			{ key: 'character', label: 'Character', desc: 'Character-specific songs' }
		] as category}
			<div class="border rounded-lg p-4 bg-gray-50">
				<div class="mb-3">
					<h4 class="font-medium text-sm">{category.label}</h4>
					<p class="text-xs text-gray-500">{category.desc}</p>
				</div>
				<div class="grid grid-cols-3 gap-3">
					{#each [
						{ key: 'openings', label: 'Openings' },
						{ key: 'endings', label: 'Endings' },
						{ key: 'inserts', label: 'Inserts' }
					] as songType}
						<div class="flex items-center space-x-2">
							<Checkbox 
								bind:checked={editedValue[category.key][songType.key]}
								id={`${category.key}-${songType.key}`}
								onchange={(checked) => handleCategoryChange(category.key, songType.key, checked)}
							/>
							<Label for={`${category.key}-${songType.key}`} class="text-sm font-normal">
								{songType.label}
							</Label>
						</div>
					{/each}
				</div>
			</div>
		{/each}
	</div>
</div>
