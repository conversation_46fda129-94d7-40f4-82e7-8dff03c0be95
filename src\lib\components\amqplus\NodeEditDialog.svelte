<!-- NodeEditDialogNew.svelte - Main dialog controller -->
<script>
	import DialogLayout from './dialog/DialogLayout.svelte';
	import SimpleFormFields from './dialog/SimpleFormFields.svelte';
	import ComplexFormFields from './dialog/ComplexFormFields.svelte';
	import SongCategoriesForm from './dialog/SongCategoriesForm.svelte';
	import { settingConfigs } from './dialog/settingsConfig.js';

	let {
		open = $bindable(false),
		nodeData = $bindable(null),
		onSave = () => {},
		onModalClose = () => {} // New callback for when modal closes
	} = $props();

	// Local state for editing
	let editedValue = $state(null);
	let isValid = $state(true);
	let validationMessage = $state('');
	let validationWarning = $state('');

	// Get node color for slider styling
	const getNodeColor = () => {
		return nodeData?.color || '#6366f1'; // Default to indigo if no color
	};

	// Simple deep clone function to avoid structuredClone issues
	function deepClone(obj) {
		if (obj === null || typeof obj !== 'object') {
			return obj;
		}
		if (obj instanceof Date) {
			return new Date(obj.getTime());
		}
		if (Array.isArray(obj)) {
			return obj.map((item) => deepClone(item));
		}
		const cloned = {};
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}

	// Watch for nodeData changes to initialize editedValue
	$effect(() => {
		if (nodeData && open) {
			const settingId = nodeData.id?.replace('-setting', '');
			const config = settingConfigs[settingId];

			if (!config) {
				editedValue = null;
				return;
			}

			// Initialize editedValue based on the setting type and current value
			// Use default value if currentValue is undefined
			const currentValue = nodeData.currentValue !== undefined ? nodeData.currentValue : config.default;

			if (config.type === 'number-with-random') {
				if (typeof currentValue === 'object' && currentValue.random !== undefined) {
					editedValue = deepClone(currentValue);
				} else {
					editedValue = { random: false, value: currentValue, min: config.min, max: config.max };
				}
			} else if (config.type === 'select-with-random') {
				if (typeof currentValue === 'object' && currentValue.random !== undefined) {
					editedValue = deepClone(currentValue);
					// Ensure all option keys exist
					if (!editedValue.options) editedValue.options = {};
					config.options?.forEach(opt => {
						if (editedValue.options[opt.value] === undefined) {
							editedValue.options[opt.value] = false;
						}
					});
				} else {
					const options = {};
					config.options?.forEach(opt => options[opt.value] = false);
					// Set first option as default
					if (config.options?.length > 0) {
						options[config.options[0].value] = true;
					}
					editedValue = {
						random: false,
						value: currentValue || config.options?.[0]?.value || null,
						options
					};
				}
			} else {
				editedValue = deepClone(currentValue);
			}
		}
	});

	// Get configuration for current node
	const config = $derived(nodeData ? settingConfigs[nodeData.id?.replace('-setting', '')] : null);

	// This determines which CSS class and layout to use.
	const dialogSize = $derived(config?.size || 'medium');

	// Get total songs setting for validation context
	const getTotalSongs = () => {
		// Try to get from nodeData context or use default
		return nodeData?.totalSongs || 20; // Default fallback
	};

	function handleSave() {
		if (!isValid || !nodeData) return;
		
		onSave({
			nodeId: nodeData.id,
			newValue: editedValue
		});
		
		open = false;
	}

	function handleCancel() {
		open = false;
		editedValue = null;
	}

	// Determine if this is a complex form type
	const isComplexForm = $derived(config?.type?.startsWith('complex-') || false);
</script>

<DialogLayout
	bind:open
	bind:nodeData
	{dialogSize}
	{isValid}
	{getNodeColor}
	onSave={handleSave}
	onCancel={handleCancel}
	{onModalClose}
>
	{#snippet children()}
		{#if config && editedValue !== null}
			{#if config.type === 'song-categories'}
				<SongCategoriesForm
					{config}
					bind:editedValue
					bind:isValid
					bind:validationMessage
				/>
			{:else if isComplexForm}
				<ComplexFormFields
					{config}
					bind:editedValue
					bind:isValid
					bind:validationMessage
					bind:validationWarning
					{getNodeColor}
					{getTotalSongs}
				/>
			{:else}
				<SimpleFormFields
					{config}
					bind:editedValue
					bind:isValid
					bind:validationMessage
					{getNodeColor}
				/>
			{/if}
		{/if}
	{/snippet}
</DialogLayout>
